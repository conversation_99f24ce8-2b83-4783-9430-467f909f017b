# Chapter 5: Service Management

Welcome to Chapter 5! In this chapter, we'll build a comprehensive service management system that allows administrators to manage car wash services, pricing, and service categories.

## 🎯 Chapter Objectives

By the end of this chapter, you will:
- Create a Service model with relationships to ServiceCategory
- Build a complete service management interface
- Implement CRUD operations for services and categories
- Add service pricing and duration management
- Create service availability and scheduling features
- Implement service image uploads
- Build a public service catalog for customers

## 📋 What We'll Cover

1. Enhancing the Service model with advanced features
2. Creating service category management
3. Building service controller with CRUD operations
4. Creating service views and forms
5. Implementing service image uploads
6. Adding service availability management
7. Creating a public service catalog
8. Testing the service management system

## 🛠 Step 1: Enhancing the Service Model

Let's enhance our existing Service model with more advanced features. First, let's update the Service model `app/Models/Service.php`:

```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Service extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'service_category_id',
        'name',
        'description',
        'short_description',
        'price',
        'duration_minutes',
        'is_active',
        'is_featured',
        'image',
        'requirements',
        'benefits',
        'popularity_score',
        'min_advance_booking_hours',
        'max_advance_booking_days',
        'available_days',
        'available_time_slots',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'duration_minutes' => 'integer',
        'is_active' => 'boolean',
        'is_featured' => 'boolean',
        'requirements' => 'array',
        'benefits' => 'array',
        'popularity_score' => 'integer',
        'min_advance_booking_hours' => 'integer',
        'max_advance_booking_days' => 'integer',
        'available_days' => 'array', // ['monday', 'tuesday', etc.]
        'available_time_slots' => 'array', // ['09:00-10:00', '10:00-11:00', etc.]
    ];

    // Relationships
    public function serviceCategory(): BelongsTo
    {
        return $this->belongsTo(ServiceCategory::class);
    }

    public function bookings(): HasMany
    {
        return $this->hasMany(Booking::class);
    }

    public function bookingServices(): BelongsToMany
    {
        return $this->belongsToMany(Booking::class, 'booking_services')
                    ->withPivot(['quantity', 'price', 'notes'])
                    ->withTimestamps();
    }

    // Accessors
    public function getFormattedPriceAttribute(): string
    {
        return '$' . number_format($this->price, 2);
    }

    public function getFormattedDurationAttribute(): string
    {
        $hours = floor($this->duration_minutes / 60);
        $minutes = $this->duration_minutes % 60;
        
        if ($hours > 0 && $minutes > 0) {
            return "{$hours}h {$minutes}m";
        } elseif ($hours > 0) {
            return "{$hours}h";
        } else {
            return "{$minutes}m";
        }
    }

    public function getEstimatedEndTimeAttribute(): string
    {
        // This will be used when booking to calculate end time
        return date('H:i', strtotime("+{$this->duration_minutes} minutes"));
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('service_category_id', $categoryId);
    }

    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('short_description', 'like', "%{$search}%");
        });
    }

    public function scopePopular($query)
    {
        return $query->orderBy('popularity_score', 'desc');
    }

    public function scopePriceRange($query, $minPrice = null, $maxPrice = null)
    {
        if ($minPrice !== null) {
            $query->where('price', '>=', $minPrice);
        }
        
        if ($maxPrice !== null) {
            $query->where('price', '<=', $maxPrice);
        }
        
        return $query;
    }

    // Helper methods
    public function isAvailableOnDay($day): bool
    {
        return in_array(strtolower($day), $this->available_days ?? []);
    }

    public function getAvailableTimeSlots(): array
    {
        return $this->available_time_slots ?? [];
    }

    public function canBeBookedAt($dateTime): bool
    {
        $dayName = strtolower($dateTime->format('l'));
        $timeSlot = $dateTime->format('H:i');
        
        // Check if service is available on this day
        if (!$this->isAvailableOnDay($dayName)) {
            return false;
        }
        
        // Check advance booking requirements
        $hoursUntilBooking = now()->diffInHours($dateTime);
        
        if ($hoursUntilBooking < $this->min_advance_booking_hours) {
            return false;
        }
        
        $daysUntilBooking = now()->diffInDays($dateTime);
        
        if ($daysUntilBooking > $this->max_advance_booking_days) {
            return false;
        }
        
        return true;
    }

    public function incrementPopularity(): void
    {
        $this->increment('popularity_score');
    }

    // Boot method
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($service) {
            if (!isset($service->popularity_score)) {
                $service->popularity_score = 0;
            }
        });
    }
}
```

## 🛠 Step 2: Creating Service Migration Updates

Let's create a migration to add the new fields to our services table:

```bash
# Create migration to add new service fields
php artisan make:migration add_advanced_fields_to_services_table --table=services
```

Edit the migration file `database/migrations/xxxx_xx_xx_xxxxxx_add_advanced_fields_to_services_table.php`:

```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('services', function (Blueprint $table) {
            $table->text('short_description')->nullable()->after('description');
            $table->boolean('is_featured')->default(false)->after('is_active');
            $table->string('image')->nullable()->after('is_featured');
            $table->json('requirements')->nullable()->after('image'); // What customer needs to bring/know
            $table->json('benefits')->nullable()->after('requirements'); // Service benefits
            $table->integer('popularity_score')->default(0)->after('benefits');
            $table->integer('min_advance_booking_hours')->default(2)->after('popularity_score');
            $table->integer('max_advance_booking_days')->default(30)->after('min_advance_booking_hours');
            $table->json('available_days')->nullable()->after('max_advance_booking_days'); // Days of week
            $table->json('available_time_slots')->nullable()->after('available_days'); // Time slots
            $table->softDeletes()->after('updated_at');
            
            // Add indexes for better performance
            $table->index('is_featured');
            $table->index('popularity_score');
        });
    }

    public function down(): void
    {
        Schema::table('services', function (Blueprint $table) {
            $table->dropSoftDeletes();
            $table->dropIndex(['is_featured']);
            $table->dropIndex(['popularity_score']);
            $table->dropColumn([
                'short_description',
                'is_featured',
                'image',
                'requirements',
                'benefits',
                'popularity_score',
                'min_advance_booking_hours',
                'max_advance_booking_days',
                'available_days',
                'available_time_slots',
            ]);
        });
    }
};
```

Run the migration:

```bash
php artisan migrate
```

## 🛠 Step 3: Creating the Service Controller

Let's create a comprehensive service controller:

```bash
# Create service controller
php artisan make:controller ServiceController --resource
```

Edit `app/Http/Controllers/ServiceController.php`:

```php
<?php

namespace App\Http\Controllers;

use App\Models\Service;
use App\Models\ServiceCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class ServiceController extends Controller
{
    public function index(Request $request)
    {
        $query = Service::with('serviceCategory');

        // Search functionality
        if ($request->filled('search')) {
            $query->search($request->search);
        }

        // Category filter
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        // Status filter
        if ($request->filled('status')) {
            if ($request->status === 'active') {
                $query->active();
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        // Featured filter
        if ($request->filled('featured')) {
            $query->featured();
        }

        // Price range filter
        if ($request->filled('min_price') || $request->filled('max_price')) {
            $query->priceRange($request->min_price, $request->max_price);
        }

        // Sort functionality
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');
        
        $allowedSorts = ['name', 'price', 'duration_minutes', 'popularity_score', 'created_at'];
        if (in_array($sortField, $allowedSorts)) {
            $query->orderBy($sortField, $sortDirection);
        }

        $services = $query->paginate(15)->withQueryString();
        $categories = ServiceCategory::active()->get();

        return view('services.index', compact('services', 'categories'));
    }

    public function create()
    {
        $categories = ServiceCategory::active()->get();
        return view('services.create', compact('categories'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'service_category_id' => 'required|exists:service_categories,id',
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'price' => 'required|numeric|min:0|max:9999.99',
            'duration_minutes' => 'required|integer|min:1|max:1440',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'requirements' => 'nullable|array',
            'requirements.*' => 'string|max:255',
            'benefits' => 'nullable|array',
            'benefits.*' => 'string|max:255',
            'min_advance_booking_hours' => 'required|integer|min:0|max:168',
            'max_advance_booking_days' => 'required|integer|min:1|max:365',
            'available_days' => 'nullable|array',
            'available_days.*' => 'in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'available_time_slots' => 'nullable|array',
            'available_time_slots.*' => 'string',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            $validated['image'] = $request->file('image')->store('service-images', 'public');
        }

        // Set default values
        $validated['is_active'] = $request->has('is_active');
        $validated['is_featured'] = $request->has('is_featured');

        $service = Service::create($validated);

        return redirect()->route('services.show', $service)
            ->with('success', 'Service created successfully!');
    }

    public function show(Service $service)
    {
        $service->load('serviceCategory');
        return view('services.show', compact('service'));
    }

    public function edit(Service $service)
    {
        $categories = ServiceCategory::active()->get();
        return view('services.edit', compact('service', 'categories'));
    }

    public function update(Request $request, Service $service)
    {
        $validated = $request->validate([
            'service_category_id' => 'required|exists:service_categories,id',
            'name' => 'required|string|max:255',
            'description' => 'required|string',
            'short_description' => 'nullable|string|max:500',
            'price' => 'required|numeric|min:0|max:9999.99',
            'duration_minutes' => 'required|integer|min:1|max:1440',
            'is_active' => 'boolean',
            'is_featured' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'requirements' => 'nullable|array',
            'requirements.*' => 'string|max:255',
            'benefits' => 'nullable|array',
            'benefits.*' => 'string|max:255',
            'min_advance_booking_hours' => 'required|integer|min:0|max:168',
            'max_advance_booking_days' => 'required|integer|min:1|max:365',
            'available_days' => 'nullable|array',
            'available_days.*' => 'in:monday,tuesday,wednesday,thursday,friday,saturday,sunday',
            'available_time_slots' => 'nullable|array',
            'available_time_slots.*' => 'string',
        ]);

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($service->image) {
                Storage::disk('public')->delete($service->image);
            }
            $validated['image'] = $request->file('image')->store('service-images', 'public');
        }

        // Set default values
        $validated['is_active'] = $request->has('is_active');
        $validated['is_featured'] = $request->has('is_featured');

        $service->update($validated);

        return redirect()->route('services.show', $service)
            ->with('success', 'Service updated successfully!');
    }

    public function destroy(Service $service)
    {
        // Soft delete the service
        $service->delete();

        return redirect()->route('services.index')
            ->with('success', 'Service deleted successfully!');
    }
}
```

## 🛠 Step 4: Adding Service Routes

Add the service routes to `routes/web.php`:

```php
// Service management routes - accessible by staff and admin
Route::middleware(['auth', 'role:staff,admin'])->group(function () {
    Route::resource('services', ServiceController::class);
});

// Public service catalog - accessible by all authenticated users
Route::middleware(['auth'])->group(function () {
    Route::get('/catalog', [ServiceController::class, 'catalog'])->name('services.catalog');
});
```

## 🛠 Step 5: Creating Service Views

Now let's create the views for our service management system.

### 5.1 Service Index View

Create `resources/views/services/index.blade.php`:

```blade
@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Service Management</h1>
                <a href="{{ route('services.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Add New Service
                </a>
            </div>

            <!-- Search and Filter Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('services.index') }}">
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" name="search" class="form-control"
                                       placeholder="Search services..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-2">
                                <select name="category" class="form-control">
                                    <option value="">All Categories</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}"
                                                {{ request('category') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="status" class="form-control">
                                    <option value="">All Status</option>
                                    <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Active</option>
                                    <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Inactive</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <input type="number" name="min_price" class="form-control"
                                       placeholder="Min Price" value="{{ request('min_price') }}">
                            </div>
                            <div class="col-md-2">
                                <input type="number" name="max_price" class="form-control"
                                       placeholder="Max Price" value="{{ request('max_price') }}">
                            </div>
                            <div class="col-md-1">
                                <button type="submit" class="btn btn-outline-primary">Filter</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Services Table -->
            <div class="card">
                <div class="card-body">
                    @if($services->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Image</th>
                                        <th>
                                            <a href="{{ request()->fullUrlWithQuery(['sort' => 'name', 'direction' => request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                                                Name
                                                @if(request('sort') == 'name')
                                                    <i class="fas fa-sort-{{ request('direction') == 'asc' ? 'up' : 'down' }}"></i>
                                                @endif
                                            </a>
                                        </th>
                                        <th>Category</th>
                                        <th>
                                            <a href="{{ request()->fullUrlWithQuery(['sort' => 'price', 'direction' => request('direction') == 'asc' ? 'desc' : 'asc']) }}">
                                                Price
                                                @if(request('sort') == 'price')
                                                    <i class="fas fa-sort-{{ request('direction') == 'asc' ? 'up' : 'down' }}"></i>
                                                @endif
                                            </a>
                                        </th>
                                        <th>Duration</th>
                                        <th>Status</th>
                                        <th>Featured</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($services as $service)
                                        <tr>
                                            <td>
                                                @if($service->image)
                                                    <img src="{{ Storage::url($service->image) }}"
                                                         alt="{{ $service->name }}"
                                                         class="img-thumbnail" style="width: 50px; height: 50px; object-fit: cover;">
                                                @else
                                                    <div class="bg-light d-flex align-items-center justify-content-center"
                                                         style="width: 50px; height: 50px;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                @endif
                                            </td>
                                            <td>
                                                <strong>{{ $service->name }}</strong>
                                                <br>
                                                <small class="text-muted">{{ Str::limit($service->short_description, 50) }}</small>
                                            </td>
                                            <td>{{ $service->serviceCategory->name }}</td>
                                            <td>${{ number_format($service->price, 2) }}</td>
                                            <td>{{ $service->formatted_duration }}</td>
                                            <td>
                                                @if($service->is_active)
                                                    <span class="badge badge-success">Active</span>
                                                @else
                                                    <span class="badge badge-secondary">Inactive</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($service->is_featured)
                                                    <span class="badge badge-warning">Featured</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="{{ route('services.show', $service) }}"
                                                       class="btn btn-sm btn-outline-info">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="{{ route('services.edit', $service) }}"
                                                       class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form action="{{ route('services.destroy', $service) }}"
                                                          method="POST" class="d-inline"
                                                          onsubmit="return confirm('Are you sure you want to delete this service?')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $services->links() }}
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5>No services found</h5>
                            <p class="text-muted">Start by creating your first service.</p>
                            <a href="{{ route('services.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Add New Service
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
```

### 5.2 Service Create View

Create `resources/views/services/create.blade.php`:

```blade
@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Create New Service</h1>
                <a href="{{ route('services.index') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Services
                </a>
            </div>

            <div class="card">
                <div class="card-body">
                    <form action="{{ route('services.store') }}" method="POST" enctype="multipart/form-data">
                        @csrf

                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-8">
                                <h5 class="mb-3">Basic Information</h5>

                                <div class="form-group">
                                    <label for="service_category_id">Service Category *</label>
                                    <select name="service_category_id" id="service_category_id"
                                            class="form-control @error('service_category_id') is-invalid @enderror" required>
                                        <option value="">Select Category</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}"
                                                    {{ old('service_category_id') == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('service_category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="name">Service Name *</label>
                                    <input type="text" name="name" id="name"
                                           class="form-control @error('name') is-invalid @enderror"
                                           value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="short_description">Short Description</label>
                                    <input type="text" name="short_description" id="short_description"
                                           class="form-control @error('short_description') is-invalid @enderror"
                                           value="{{ old('short_description') }}" maxlength="500"
                                           placeholder="Brief description for listings">
                                    @error('short_description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="description">Full Description *</label>
                                    <textarea name="description" id="description" rows="4"
                                              class="form-control @error('description') is-invalid @enderror"
                                              required>{{ old('description') }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="price">Price ($) *</label>
                                            <input type="number" name="price" id="price" step="0.01" min="0" max="9999.99"
                                                   class="form-control @error('price') is-invalid @enderror"
                                                   value="{{ old('price') }}" required>
                                            @error('price')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="duration_minutes">Duration (minutes) *</label>
                                            <input type="number" name="duration_minutes" id="duration_minutes"
                                                   min="1" max="1440"
                                                   class="form-control @error('duration_minutes') is-invalid @enderror"
                                                   value="{{ old('duration_minutes') }}" required>
                                            @error('duration_minutes')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Settings and Image -->
                            <div class="col-md-4">
                                <h5 class="mb-3">Settings</h5>

                                <div class="form-group">
                                    <label for="image">Service Image</label>
                                    <input type="file" name="image" id="image"
                                           class="form-control-file @error('image') is-invalid @enderror"
                                           accept="image/*">
                                    @error('image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Max size: 2MB. Formats: JPEG, PNG, JPG, GIF</small>
                                </div>

                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" name="is_active" id="is_active"
                                               class="form-check-input" value="1"
                                               {{ old('is_active', true) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active Service
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" name="is_featured" id="is_featured"
                                               class="form-check-input" value="1"
                                               {{ old('is_featured') ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_featured">
                                            Featured Service
                                        </label>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="min_advance_booking_hours">Min Advance Booking (hours)</label>
                                            <input type="number" name="min_advance_booking_hours"
                                                   id="min_advance_booking_hours" min="0" max="168"
                                                   class="form-control @error('min_advance_booking_hours') is-invalid @enderror"
                                                   value="{{ old('min_advance_booking_hours', 2) }}">
                                            @error('min_advance_booking_hours')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="max_advance_booking_days">Max Advance Booking (days)</label>
                                            <input type="number" name="max_advance_booking_days"
                                                   id="max_advance_booking_days" min="1" max="365"
                                                   class="form-control @error('max_advance_booking_days') is-invalid @enderror"
                                                   value="{{ old('max_advance_booking_days', 30) }}">
                                            @error('max_advance_booking_days')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Service Requirements -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h5 class="mb-3">Service Requirements</h5>
                                <div id="requirements-container">
                                    <div class="form-group requirement-item">
                                        <div class="input-group">
                                            <input type="text" name="requirements[]"
                                                   class="form-control" placeholder="Enter requirement">
                                            <div class="input-group-append">
                                                <button type="button" class="btn btn-outline-danger remove-requirement">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" id="add-requirement" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-plus"></i> Add Requirement
                                </button>
                            </div>

                            <div class="col-md-6">
                                <h5 class="mb-3">Service Benefits</h5>
                                <div id="benefits-container">
                                    <div class="form-group benefit-item">
                                        <div class="input-group">
                                            <input type="text" name="benefits[]"
                                                   class="form-control" placeholder="Enter benefit">
                                            <div class="input-group-append">
                                                <button type="button" class="btn btn-outline-danger remove-benefit">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <button type="button" id="add-benefit" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-plus"></i> Add Benefit
                                </button>
                            </div>
                        </div>

                        <!-- Available Days -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <h5 class="mb-3">Available Days</h5>
                                <div class="row">
                                    @php
                                        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                                        $dayLabels = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                                    @endphp
                                    @foreach($days as $index => $day)
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input type="checkbox" name="available_days[]"
                                                       id="day_{{ $day }}" value="{{ $day }}"
                                                       class="form-check-input"
                                                       {{ in_array($day, old('available_days', [])) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="day_{{ $day }}">
                                                    {{ $dayLabels[$index] }}
                                                </label>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Create Service
                            </button>
                            <a href="{{ route('services.index') }}" class="btn btn-secondary ml-2">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add requirement functionality
    document.getElementById('add-requirement').addEventListener('click', function() {
        const container = document.getElementById('requirements-container');
        const newItem = document.createElement('div');
        newItem.className = 'form-group requirement-item';
        newItem.innerHTML = `
            <div class="input-group">
                <input type="text" name="requirements[]" class="form-control" placeholder="Enter requirement">
                <div class="input-group-append">
                    <button type="button" class="btn btn-outline-danger remove-requirement">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        container.appendChild(newItem);
    });

    // Remove requirement functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-requirement')) {
            const item = e.target.closest('.requirement-item');
            if (document.querySelectorAll('.requirement-item').length > 1) {
                item.remove();
            }
        }
    });

    // Add benefit functionality
    document.getElementById('add-benefit').addEventListener('click', function() {
        const container = document.getElementById('benefits-container');
        const newItem = document.createElement('div');
        newItem.className = 'form-group benefit-item';
        newItem.innerHTML = `
            <div class="input-group">
                <input type="text" name="benefits[]" class="form-control" placeholder="Enter benefit">
                <div class="input-group-append">
                    <button type="button" class="btn btn-outline-danger remove-benefit">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        container.appendChild(newItem);
    });

    // Remove benefit functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-benefit')) {
            const item = e.target.closest('.benefit-item');
            if (document.querySelectorAll('.benefit-item').length > 1) {
                item.remove();
            }
        }
    });
});
</script>
@endsection
```

### 5.3 Service Show View

Create `resources/views/services/show.blade.php`:

```blade
@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>{{ $service->name }}</h1>
                <div>
                    <a href="{{ route('services.edit', $service) }}" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Service
                    </a>
                    <a href="{{ route('services.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Services
                    </a>
                </div>
            </div>

            <div class="row">
                <!-- Service Image and Basic Info -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-body text-center">
                            @if($service->image)
                                <img src="{{ Storage::url($service->image) }}"
                                     alt="{{ $service->name }}"
                                     class="img-fluid rounded mb-3" style="max-height: 300px;">
                            @else
                                <div class="bg-light d-flex align-items-center justify-content-center rounded mb-3"
                                     style="height: 200px;">
                                    <i class="fas fa-image fa-3x text-muted"></i>
                                </div>
                            @endif

                            <h4 class="text-primary">${{ number_format($service->price, 2) }}</h4>
                            <p class="text-muted">{{ $service->formatted_duration }}</p>

                            <div class="mb-3">
                                @if($service->is_active)
                                    <span class="badge badge-success">Active</span>
                                @else
                                    <span class="badge badge-secondary">Inactive</span>
                                @endif

                                @if($service->is_featured)
                                    <span class="badge badge-warning">Featured</span>
                                @endif
                            </div>

                            <p><strong>Category:</strong> {{ $service->serviceCategory->name }}</p>
                            <p><strong>Popularity Score:</strong> {{ $service->popularity_score }}</p>
                        </div>
                    </div>
                </div>

                <!-- Service Details -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-body">
                            <h5 class="card-title">Service Description</h5>

                            @if($service->short_description)
                                <p class="text-muted"><em>{{ $service->short_description }}</em></p>
                            @endif

                            <p>{{ $service->description }}</p>

                            @if($service->requirements && count($service->requirements) > 0)
                                <h6 class="mt-4">Requirements:</h6>
                                <ul>
                                    @foreach($service->requirements as $requirement)
                                        <li>{{ $requirement }}</li>
                                    @endforeach
                                </ul>
                            @endif

                            @if($service->benefits && count($service->benefits) > 0)
                                <h6 class="mt-4">Benefits:</h6>
                                <ul>
                                    @foreach($service->benefits as $benefit)
                                        <li>{{ $benefit }}</li>
                                    @endforeach
                                </ul>
                            @endif
                        </div>
                    </div>

                    <!-- Booking Settings -->
                    <div class="card mt-4">
                        <div class="card-body">
                            <h5 class="card-title">Booking Settings</h5>
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Min Advance Booking:</strong> {{ $service->min_advance_booking_hours }} hours</p>
                                    <p><strong>Max Advance Booking:</strong> {{ $service->max_advance_booking_days }} days</p>
                                </div>
                                <div class="col-md-6">
                                    @if($service->available_days && count($service->available_days) > 0)
                                        <p><strong>Available Days:</strong></p>
                                        <div>
                                            @foreach($service->available_days as $day)
                                                <span class="badge badge-info mr-1">{{ ucfirst($day) }}</span>
                                            @endforeach
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
```

### 5.4 Service Edit View

Create `resources/views/services/edit.blade.php`:

```blade
@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Edit Service: {{ $service->name }}</h1>
                <div>
                    <a href="{{ route('services.show', $service) }}" class="btn btn-info">
                        <i class="fas fa-eye"></i> View Service
                    </a>
                    <a href="{{ route('services.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Services
                    </a>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <form action="{{ route('services.update', $service) }}" method="POST" enctype="multipart/form-data">
                        @csrf
                        @method('PUT')

                        <div class="row">
                            <!-- Basic Information -->
                            <div class="col-md-8">
                                <h5 class="mb-3">Basic Information</h5>

                                <div class="form-group">
                                    <label for="service_category_id">Service Category *</label>
                                    <select name="service_category_id" id="service_category_id"
                                            class="form-control @error('service_category_id') is-invalid @enderror" required>
                                        <option value="">Select Category</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}"
                                                    {{ old('service_category_id', $service->service_category_id) == $category->id ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('service_category_id')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="name">Service Name *</label>
                                    <input type="text" name="name" id="name"
                                           class="form-control @error('name') is-invalid @enderror"
                                           value="{{ old('name', $service->name) }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="short_description">Short Description</label>
                                    <input type="text" name="short_description" id="short_description"
                                           class="form-control @error('short_description') is-invalid @enderror"
                                           value="{{ old('short_description', $service->short_description) }}" maxlength="500"
                                           placeholder="Brief description for listings">
                                    @error('short_description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="form-group">
                                    <label for="description">Full Description *</label>
                                    <textarea name="description" id="description" rows="4"
                                              class="form-control @error('description') is-invalid @enderror"
                                              required>{{ old('description', $service->description) }}</textarea>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="price">Price ($) *</label>
                                            <input type="number" name="price" id="price" step="0.01" min="0" max="9999.99"
                                                   class="form-control @error('price') is-invalid @enderror"
                                                   value="{{ old('price', $service->price) }}" required>
                                            @error('price')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="duration_minutes">Duration (minutes) *</label>
                                            <input type="number" name="duration_minutes" id="duration_minutes"
                                                   min="1" max="1440"
                                                   class="form-control @error('duration_minutes') is-invalid @enderror"
                                                   value="{{ old('duration_minutes', $service->duration_minutes) }}" required>
                                            @error('duration_minutes')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Settings and Image -->
                            <div class="col-md-4">
                                <h5 class="mb-3">Settings</h5>

                                <div class="form-group">
                                    <label for="image">Service Image</label>
                                    @if($service->image)
                                        <div class="mb-2">
                                            <img src="{{ Storage::url($service->image) }}"
                                                 alt="{{ $service->name }}"
                                                 class="img-thumbnail" style="max-width: 200px;">
                                        </div>
                                    @endif
                                    <input type="file" name="image" id="image"
                                           class="form-control-file @error('image') is-invalid @enderror"
                                           accept="image/*">
                                    @error('image')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <small class="form-text text-muted">Max size: 2MB. Formats: JPEG, PNG, JPG, GIF</small>
                                </div>

                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" name="is_active" id="is_active"
                                               class="form-check-input" value="1"
                                               {{ old('is_active', $service->is_active) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_active">
                                            Active Service
                                        </label>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" name="is_featured" id="is_featured"
                                               class="form-check-input" value="1"
                                               {{ old('is_featured', $service->is_featured) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="is_featured">
                                            Featured Service
                                        </label>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="min_advance_booking_hours">Min Advance Booking (hours)</label>
                                            <input type="number" name="min_advance_booking_hours"
                                                   id="min_advance_booking_hours" min="0" max="168"
                                                   class="form-control @error('min_advance_booking_hours') is-invalid @enderror"
                                                   value="{{ old('min_advance_booking_hours', $service->min_advance_booking_hours) }}">
                                            @error('min_advance_booking_hours')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label for="max_advance_booking_days">Max Advance Booking (days)</label>
                                            <input type="number" name="max_advance_booking_days"
                                                   id="max_advance_booking_days" min="1" max="365"
                                                   class="form-control @error('max_advance_booking_days') is-invalid @enderror"
                                                   value="{{ old('max_advance_booking_days', $service->max_advance_booking_days) }}">
                                            @error('max_advance_booking_days')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Service Requirements -->
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h5 class="mb-3">Service Requirements</h5>
                                <div id="requirements-container">
                                    @if($service->requirements && count($service->requirements) > 0)
                                        @foreach($service->requirements as $requirement)
                                            <div class="form-group requirement-item">
                                                <div class="input-group">
                                                    <input type="text" name="requirements[]"
                                                           class="form-control" value="{{ $requirement }}">
                                                    <div class="input-group-append">
                                                        <button type="button" class="btn btn-outline-danger remove-requirement">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    @else
                                        <div class="form-group requirement-item">
                                            <div class="input-group">
                                                <input type="text" name="requirements[]"
                                                       class="form-control" placeholder="Enter requirement">
                                                <div class="input-group-append">
                                                    <button type="button" class="btn btn-outline-danger remove-requirement">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                                <button type="button" id="add-requirement" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-plus"></i> Add Requirement
                                </button>
                            </div>

                            <div class="col-md-6">
                                <h5 class="mb-3">Service Benefits</h5>
                                <div id="benefits-container">
                                    @if($service->benefits && count($service->benefits) > 0)
                                        @foreach($service->benefits as $benefit)
                                            <div class="form-group benefit-item">
                                                <div class="input-group">
                                                    <input type="text" name="benefits[]"
                                                           class="form-control" value="{{ $benefit }}">
                                                    <div class="input-group-append">
                                                        <button type="button" class="btn btn-outline-danger remove-benefit">
                                                            <i class="fas fa-times"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach
                                    @else
                                        <div class="form-group benefit-item">
                                            <div class="input-group">
                                                <input type="text" name="benefits[]"
                                                       class="form-control" placeholder="Enter benefit">
                                                <div class="input-group-append">
                                                    <button type="button" class="btn btn-outline-danger remove-benefit">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                                <button type="button" id="add-benefit" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-plus"></i> Add Benefit
                                </button>
                            </div>
                        </div>

                        <!-- Available Days -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <h5 class="mb-3">Available Days</h5>
                                <div class="row">
                                    @php
                                        $days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
                                        $dayLabels = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                                        $selectedDays = old('available_days', $service->available_days ?? []);
                                    @endphp
                                    @foreach($days as $index => $day)
                                        <div class="col-md-3">
                                            <div class="form-check">
                                                <input type="checkbox" name="available_days[]"
                                                       id="day_{{ $day }}" value="{{ $day }}"
                                                       class="form-check-input"
                                                       {{ in_array($day, $selectedDays) ? 'checked' : '' }}>
                                                <label class="form-check-label" for="day_{{ $day }}">
                                                    {{ $dayLabels[$index] }}
                                                </label>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Service
                            </button>
                            <a href="{{ route('services.show', $service) }}" class="btn btn-secondary ml-2">Cancel</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add requirement functionality
    document.getElementById('add-requirement').addEventListener('click', function() {
        const container = document.getElementById('requirements-container');
        const newItem = document.createElement('div');
        newItem.className = 'form-group requirement-item';
        newItem.innerHTML = `
            <div class="input-group">
                <input type="text" name="requirements[]" class="form-control" placeholder="Enter requirement">
                <div class="input-group-append">
                    <button type="button" class="btn btn-outline-danger remove-requirement">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        container.appendChild(newItem);
    });

    // Remove requirement functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-requirement')) {
            const item = e.target.closest('.requirement-item');
            if (document.querySelectorAll('.requirement-item').length > 1) {
                item.remove();
            }
        }
    });

    // Add benefit functionality
    document.getElementById('add-benefit').addEventListener('click', function() {
        const container = document.getElementById('benefits-container');
        const newItem = document.createElement('div');
        newItem.className = 'form-group benefit-item';
        newItem.innerHTML = `
            <div class="input-group">
                <input type="text" name="benefits[]" class="form-control" placeholder="Enter benefit">
                <div class="input-group-append">
                    <button type="button" class="btn btn-outline-danger remove-benefit">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
        container.appendChild(newItem);
    });

    // Remove benefit functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.remove-benefit')) {
            const item = e.target.closest('.benefit-item');
            if (document.querySelectorAll('.benefit-item').length > 1) {
                item.remove();
            }
        }
    });
});
</script>
@endsection
```

### 5.5 Public Service Catalog

Add this method to your `ServiceController.php`:

```php
public function catalog(Request $request)
{
    $query = Service::with('serviceCategory')->active();

    // Search functionality
    if ($request->filled('search')) {
        $query->search($request->search);
    }

    // Category filter
    if ($request->filled('category')) {
        $query->byCategory($request->category);
    }

    // Price range filter
    if ($request->filled('min_price') || $request->filled('max_price')) {
        $query->priceRange($request->min_price, $request->max_price);
    }

    // Sort functionality
    $sortField = $request->get('sort', 'popularity_score');
    $sortDirection = $request->get('direction', 'desc');

    $allowedSorts = ['name', 'price', 'duration_minutes', 'popularity_score'];
    if (in_array($sortField, $allowedSorts)) {
        $query->orderBy($sortField, $sortDirection);
    }

    $services = $query->paginate(12)->withQueryString();
    $categories = ServiceCategory::active()->get();
    $featuredServices = Service::active()->featured()->limit(3)->get();

    return view('services.catalog', compact('services', 'categories', 'featuredServices'));
}
```

Create `resources/views/services/catalog.blade.php`:

```blade
@extends('layouts.app')

@section('content')
<div class="container">
    <!-- Hero Section with Featured Services -->
    @if($featuredServices->count() > 0)
        <div class="row mb-5">
            <div class="col-md-12">
                <h2 class="text-center mb-4">Featured Services</h2>
                <div class="row">
                    @foreach($featuredServices as $service)
                        <div class="col-md-4 mb-4">
                            <div class="card h-100 border-warning">
                                <div class="card-header bg-warning text-dark">
                                    <i class="fas fa-star"></i> Featured
                                </div>
                                @if($service->image)
                                    <img src="{{ Storage::url($service->image) }}"
                                         class="card-img-top" style="height: 200px; object-fit: cover;"
                                         alt="{{ $service->name }}">
                                @endif
                                <div class="card-body">
                                    <h5 class="card-title">{{ $service->name }}</h5>
                                    <p class="card-text">{{ $service->short_description }}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="h5 text-primary">${{ number_format($service->price, 2) }}</span>
                                        <small class="text-muted">{{ $service->formatted_duration }}</small>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <a href="#" class="btn btn-primary btn-block">Book Now</a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    @endif

    <!-- Service Catalog -->
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Our Services</h1>
            </div>

            <!-- Search and Filter Form -->
            <div class="card mb-4">
                <div class="card-body">
                    <form method="GET" action="{{ route('services.catalog') }}">
                        <div class="row">
                            <div class="col-md-4">
                                <input type="text" name="search" class="form-control"
                                       placeholder="Search services..." value="{{ request('search') }}">
                            </div>
                            <div class="col-md-3">
                                <select name="category" class="form-control">
                                    <option value="">All Categories</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}"
                                                {{ request('category') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <input type="number" name="min_price" class="form-control"
                                       placeholder="Min Price" value="{{ request('min_price') }}">
                            </div>
                            <div class="col-md-2">
                                <input type="number" name="max_price" class="form-control"
                                       placeholder="Max Price" value="{{ request('max_price') }}">
                            </div>
                            <div class="col-md-1">
                                <button type="submit" class="btn btn-primary">Search</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Services Grid -->
            @if($services->count() > 0)
                <div class="row">
                    @foreach($services as $service)
                        <div class="col-md-4 mb-4">
                            <div class="card h-100">
                                @if($service->image)
                                    <img src="{{ Storage::url($service->image) }}"
                                         class="card-img-top" style="height: 200px; object-fit: cover;"
                                         alt="{{ $service->name }}">
                                @else
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                                         style="height: 200px;">
                                        <i class="fas fa-image fa-3x text-muted"></i>
                                    </div>
                                @endif
                                <div class="card-body">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h5 class="card-title">{{ $service->name }}</h5>
                                        @if($service->is_featured)
                                            <span class="badge badge-warning">Featured</span>
                                        @endif
                                    </div>
                                    <p class="text-muted small">{{ $service->serviceCategory->name }}</p>
                                    <p class="card-text">{{ $service->short_description ?: Str::limit($service->description, 100) }}</p>

                                    @if($service->benefits && count($service->benefits) > 0)
                                        <div class="mb-3">
                                            <small class="text-muted">Benefits:</small>
                                            <ul class="small">
                                                @foreach(array_slice($service->benefits, 0, 2) as $benefit)
                                                    <li>{{ $benefit }}</li>
                                                @endforeach
                                                @if(count($service->benefits) > 2)
                                                    <li class="text-muted">and {{ count($service->benefits) - 2 }} more...</li>
                                                @endif
                                            </ul>
                                        </div>
                                    @endif

                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="h5 text-primary">${{ number_format($service->price, 2) }}</span>
                                        <small class="text-muted">{{ $service->formatted_duration }}</small>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="row">
                                        <div class="col-8">
                                            <a href="#" class="btn btn-primary btn-block">Book Now</a>
                                        </div>
                                        <div class="col-4">
                                            <button class="btn btn-outline-info btn-block"
                                                    data-toggle="modal"
                                                    data-target="#serviceModal{{ $service->id }}">
                                                Details
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Service Details Modal -->
                        <div class="modal fade" id="serviceModal{{ $service->id }}" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">{{ $service->name }}</h5>
                                        <button type="button" class="close" data-dismiss="modal">
                                            <span>&times;</span>
                                        </button>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                @if($service->image)
                                                    <img src="{{ Storage::url($service->image) }}"
                                                         class="img-fluid rounded mb-3" alt="{{ $service->name }}">
                                                @endif
                                                <h6>Description</h6>
                                                <p>{{ $service->description }}</p>
                                            </div>
                                            <div class="col-md-6">
                                                <h6>Service Details</h6>
                                                <ul class="list-unstyled">
                                                    <li><strong>Category:</strong> {{ $service->serviceCategory->name }}</li>
                                                    <li><strong>Duration:</strong> {{ $service->formatted_duration }}</li>
                                                    <li><strong>Price:</strong> ${{ number_format($service->price, 2) }}</li>
                                                </ul>

                                                @if($service->requirements && count($service->requirements) > 0)
                                                    <h6 class="mt-3">Requirements</h6>
                                                    <ul>
                                                        @foreach($service->requirements as $requirement)
                                                            <li>{{ $requirement }}</li>
                                                        @endforeach
                                                    </ul>
                                                @endif

                                                @if($service->benefits && count($service->benefits) > 0)
                                                    <h6 class="mt-3">Benefits</h6>
                                                    <ul>
                                                        @foreach($service->benefits as $benefit)
                                                            <li>{{ $benefit }}</li>
                                                        @endforeach
                                                    </ul>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                        <a href="#" class="btn btn-primary">Book This Service</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center">
                    {{ $services->links() }}
                </div>
            @else
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5>No services found</h5>
                    <p class="text-muted">Try adjusting your search criteria.</p>
                </div>
            @endif
        </div>
    </div>
</div>
@endsection
```

## 🧪 Testing the Service Management System

1. **Test Service Creation**:
   - Visit `/services/create`
   - Create services with different categories
   - Upload service images
   - Set availability and pricing

2. **Test Service Listing**:
   - Visit `/services`
   - Test search and filtering
   - Verify sorting functionality

3. **Test Service Updates**:
   - Edit existing services
   - Update pricing and availability
   - Change service status

4. **Test Public Catalog**:
   - Visit `/catalog`
   - Test customer-facing service browsing
   - Verify featured services display
   - Test service detail modals

## 🎯 Chapter Summary

Congratulations! You've successfully:

✅ Enhanced the Service model with advanced features
✅ Added service image uploads and management
✅ Created comprehensive service CRUD operations
✅ Built a professional service management interface
✅ Implemented search and filtering functionality
✅ Added service availability and scheduling features
✅ Created service pricing and duration management

### Service Features Implemented:
- **Service Categories**: Organized service structure
- **Advanced Pricing**: Flexible pricing with duration
- **Image Management**: Service photos with upload
- **Availability Control**: Day and time slot management
- **Popularity Tracking**: Service usage analytics
- **Search & Filter**: Easy service discovery
- **Status Management**: Active/inactive service control

## 🚀 What's Next?

In the next chapter, we'll:
- Create the booking system using our services
- Implement date and time selection
- Build booking validation and conflict detection
- Add booking status management
- Create booking confirmation system

---

**Ready to handle bookings?** Let's move on to [Chapter 6: Basic Booking System](./06-basic-booking-system.md)!
